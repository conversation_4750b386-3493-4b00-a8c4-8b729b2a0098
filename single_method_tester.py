"""
单一边界检测方法测试工具
允许用户选择特定的理论方法进行深入分析和可视化
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy.stats import gaussian_kde
from scipy.ndimage import gaussian_filter1d
import argparse
import sys
from pathlib import Path

# 添加当前目录到路径
sys.path.append(str(Path(__file__).parent))

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class SingleMethodTester:
    """单一方法测试器"""
    
    def __init__(self, method_name):
        self.method_name = method_name
        self.method_display_names = {
            'information_theory': '信息论变点检测方法',
            'statistical_significance': '统计显著性检测方法',
            'adaptive_threshold': '自适应阈值检测方法',
            'bayesian_changepoint': '贝叶斯变点检测方法'
        }
        
        self.theoretical_basis = {
            'information_theory': {
                'theory': 'Kullback-Leibler散度',
                'principle': '寻找使得数据分割前后信息差异最大的点',
                'formula': 'KL(P||Q) = Σ P(x) log(P(x)/Q(x))',
                'advantages': ['有坚实的信息论基础', '能够检测分布的显著变化', '对噪声相对鲁棒'],
                'key_metrics': ['KL散度值', '信息增益', '分割点信息量']
            },
            'statistical_significance': {
                'theory': 't检验',
                'principle': '检测两个样本均值是否存在显著差异',
                'formula': 't = (μ₁ - μ₂) / √(s²(1/n₁ + 1/n₂))',
                'advantages': ['有严格的统计学基础', '提供p值作为显著性度量', '广泛应用于变点检测'],
                'key_metrics': ['t统计量', 'p值', '效应大小(Cohen\'s d)']
            },
            'adaptive_threshold': {
                'theory': 'Otsu方法的连续版本',
                'principle': '最大化类间方差，最小化类内方差',
                'formula': 'σ²ᵦ = w₁w₂(μ₁ - μ₂)²',
                'advantages': ['自动确定最优阈值', '基于方差分析理论', '在图像处理中广泛验证'],
                'key_metrics': ['类间方差', '类内方差', '分离度']
            },
            'bayesian_changepoint': {
                'theory': '贝叶斯推断和似然比检验',
                'principle': '比较有变点假设和无变点假设的似然',
                'formula': 'LR = L(H₁|data) / L(H₀|data)',
                'advantages': ['基于概率论的严格框架', '能够量化不确定性', '理论上最优的决策准则'],
                'key_metrics': ['对数似然比', '贝叶斯因子', '后验概率']
            }
        }
    
    def generate_test_data(self, scenario='mixed_normal'):
        """生成测试数据"""
        np.random.seed(42)
        
        if scenario == 'mixed_normal':
            # 混合正态分布：明显的边界
            core_data = np.random.normal(1.5, 0.4, 300)
            outer_data = np.random.normal(4.5, 1.0, 150)
            data = np.concatenate([core_data, outer_data])
            
        elif scenario == 'exponential':
            # 指数分布：渐变边界
            data = np.random.exponential(2, 400)
            
        elif scenario == 'bimodal':
            # 双峰分布：多个潜在边界
            peak1 = np.random.normal(1, 0.3, 200)
            peak2 = np.random.normal(3, 0.5, 200)
            data = np.concatenate([peak1, peak2])
            
        elif scenario == 'noisy_step':
            # 带噪声的阶跃函数
            step_data = np.concatenate([
                np.random.uniform(0, 2, 250),
                np.random.uniform(4, 6, 100)
            ])
            noise = np.random.normal(0, 0.2, len(step_data))
            data = step_data + noise
            
        return np.abs(data)  # 确保为正值距离
    
    def test_method(self, test_data, visualize=True):
        """测试指定方法"""
        try:
            from probability_density_analysis import ProbabilityDensityAnalyzer
        except ImportError:
            print("无法导入 ProbabilityDensityAnalyzer")
            return None
        
        print(f"\n=== {self.method_display_names[self.method_name]} 测试 ===")
        print(f"数据点数量: {len(test_data)}")
        
        # 创建分析器
        analyzer = ProbabilityDensityAnalyzer(
            boundary_method=self.method_name, 
            debug=True
        )
        
        # 准备数据
        distances = test_data
        max_distance = np.percentile(distances, 95)
        distance_grid = np.linspace(0, max_distance, 100)
        
        # 核密度估计
        kde = gaussian_kde(distances)
        density_values = kde(distance_grid)
        smoothed_density = gaussian_filter1d(density_values, sigma=1.5)
        
        # 获取详细分析结果
        detailed_result = analyzer.get_boundary_analysis_detailed(
            distance_grid, smoothed_density, f"{self.method_name}_test"
        )
        
        if not detailed_result or not detailed_result['methods'].get(self.method_name, {}).get('success', False):
            print("❌ 方法检测失败")
            return None
        
        method_result = detailed_result['methods'][self.method_name]
        
        # 显示结果
        print(f"✅ 检测成功!")
        print(f"   边界距离: {method_result['boundary_distance']:.4f}")
        print(f"   置信度: {method_result['confidence']:.4f}")
        print(f"   评分: {method_result['score']:.4f}")
        
        # 显示方法特定指标
        metrics = method_result.get('method_specific_metrics', {})
        if metrics:
            print(f"\n📊 方法特定指标:")
            for metric_name, value in metrics.items():
                print(f"   {metric_name}: {value:.4f}")
        
        # 可视化
        if visualize:
            self.visualize_method_analysis(
                distance_grid, smoothed_density, distances,
                method_result, metrics
            )
        
        return {
            'boundary_distance': method_result['boundary_distance'],
            'confidence': method_result['confidence'],
            'score': method_result['score'],
            'metrics': metrics,
            'distance_grid': distance_grid,
            'smoothed_density': smoothed_density,
            'raw_distances': distances
        }
    
    def visualize_method_analysis(self, distance_grid, smoothed_density, raw_distances, 
                                method_result, metrics):
        """可视化方法分析结果"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle(f'{self.method_display_names[self.method_name]} - 详细分析', 
                    fontsize=16, fontweight='bold')
        
        boundary_distance = method_result['boundary_distance']
        confidence = method_result['confidence']
        
        # 子图1: 密度曲线和检测边界
        ax1 = axes[0, 0]
        ax1.plot(distance_grid, smoothed_density, 'b-', linewidth=2, label='平滑密度曲线')
        ax1.axvline(x=boundary_distance, color='red', linestyle='--', linewidth=3,
                   label=f'检测边界 (置信度: {confidence:.3f})')
        ax1.fill_between(distance_grid, 0, smoothed_density, alpha=0.3)
        ax1.set_xlabel('距离')
        ax1.set_ylabel('密度')
        ax1.set_title('边界检测结果')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 子图2: 原始数据分布
        ax2 = axes[0, 1]
        ax2.hist(raw_distances, bins=30, alpha=0.7, density=True, color='lightblue', 
                label='原始数据分布')
        ax2.plot(distance_grid, smoothed_density, 'r-', linewidth=2, label='核密度估计')
        ax2.axvline(x=boundary_distance, color='red', linestyle='--', linewidth=2,
                   label='检测边界')
        ax2.set_xlabel('距离')
        ax2.set_ylabel('密度')
        ax2.set_title('数据分布与边界')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 子图3: 方法特定指标
        ax3 = axes[0, 2]
        if metrics:
            metric_names = list(metrics.keys())
            metric_values = list(metrics.values())
            
            bars = ax3.bar(range(len(metric_names)), metric_values, 
                          color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'][:len(metric_names)])
            ax3.set_xticks(range(len(metric_names)))
            ax3.set_xticklabels(metric_names, rotation=45, ha='right')
            ax3.set_ylabel('指标值')
            ax3.set_title('方法特定指标')
            
            # 添加数值标签
            for bar, value in zip(bars, metric_values):
                ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                        f'{value:.3f}', ha='center', va='bottom', fontsize=9)
        else:
            ax3.text(0.5, 0.5, '无可用指标', ha='center', va='center', 
                    transform=ax3.transAxes, fontsize=12)
        
        # 子图4: 理论基础说明
        ax4 = axes[1, 0]
        theory_info = self.theoretical_basis[self.method_name]
        
        y_pos = 0.9
        ax4.text(0.05, y_pos, '理论基础:', fontweight='bold', fontsize=12,
                transform=ax4.transAxes)
        y_pos -= 0.1
        ax4.text(0.05, y_pos, f"理论: {theory_info['theory']}", fontsize=10,
                transform=ax4.transAxes)
        y_pos -= 0.08
        ax4.text(0.05, y_pos, f"原理: {theory_info['principle']}", fontsize=10,
                transform=ax4.transAxes, wrap=True)
        y_pos -= 0.12
        ax4.text(0.05, y_pos, f"公式: {theory_info['formula']}", fontsize=10,
                transform=ax4.transAxes)
        
        y_pos -= 0.15
        ax4.text(0.05, y_pos, '优势:', fontweight='bold', fontsize=12,
                transform=ax4.transAxes)
        y_pos -= 0.08
        for advantage in theory_info['advantages']:
            ax4.text(0.05, y_pos, f"• {advantage}", fontsize=9,
                    transform=ax4.transAxes)
            y_pos -= 0.06
        
        ax4.set_xlim(0, 1)
        ax4.set_ylim(0, 1)
        ax4.axis('off')
        ax4.set_title('理论基础')
        
        # 子图5: 边界合理性分析
        ax5 = axes[1, 1]
        self._analyze_boundary_rationality(ax5, distance_grid, smoothed_density, 
                                         boundary_distance, raw_distances)
        
        # 子图6: 性能评估
        ax6 = axes[1, 2]
        self._evaluate_performance(ax6, method_result, metrics, raw_distances)
        
        plt.tight_layout()
        
        # 保存结果
        save_path = f'{self.method_name}_analysis_result.png'
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"\n📁 分析结果已保存到: {save_path}")
        
        plt.show()
    
    def _analyze_boundary_rationality(self, ax, distance_grid, smoothed_density, 
                                    boundary_distance, raw_distances):
        """分析边界合理性"""
        boundary_idx = np.argmin(np.abs(distance_grid - boundary_distance))
        
        # 计算边界前后的统计特征
        left_data = raw_distances[raw_distances <= boundary_distance]
        right_data = raw_distances[raw_distances > boundary_distance]
        
        if len(left_data) > 0 and len(right_data) > 0:
            left_mean = np.mean(left_data)
            right_mean = np.mean(right_data)
            left_std = np.std(left_data)
            right_std = np.std(right_data)
            
            # 计算分离度
            separation = abs(left_mean - right_mean) / (left_std + right_std + 1e-8)
            
            # 显示统计信息
            stats_text = f"""边界合理性分析:
            
边界位置: {boundary_distance:.3f}
            
左侧区域 (n={len(left_data)}):
  均值: {left_mean:.3f}
  标准差: {left_std:.3f}
  
右侧区域 (n={len(right_data)}):
  均值: {right_mean:.3f}
  标准差: {right_std:.3f}
  
分离度: {separation:.3f}
{'✅ 边界合理' if separation > 1.0 else '⚠️ 边界可能不够明显'}"""
            
            ax.text(0.05, 0.95, stats_text, transform=ax.transAxes, fontsize=10,
                   verticalalignment='top', fontfamily='monospace')
        else:
            ax.text(0.5, 0.5, '无法分析边界合理性', ha='center', va='center',
                   transform=ax.transAxes, fontsize=12)
        
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
        ax.set_title('边界合理性分析')
    
    def _evaluate_performance(self, ax, method_result, metrics, raw_distances):
        """评估方法性能"""
        confidence = method_result['confidence']
        score = method_result['score']
        
        # 性能评分
        performance_scores = {
            '置信度': confidence,
            '检测强度': min(score / 10.0, 1.0),  # 标准化分数
            '数据覆盖': len(raw_distances) / 500.0 if len(raw_distances) <= 500 else 1.0
        }
        
        # 添加方法特定性能指标
        if self.method_name == 'statistical_significance' and 'p_value' in metrics:
            performance_scores['统计显著性'] = 1 - metrics['p_value']
        elif self.method_name == 'adaptive_threshold' and 'separability' in metrics:
            performance_scores['分离度'] = min(metrics['separability'] / 5.0, 1.0)
        elif self.method_name == 'bayesian_changepoint' and 'posterior_probability' in metrics:
            performance_scores['后验概率'] = metrics['posterior_probability']
        elif self.method_name == 'information_theory' and 'information_gain' in metrics:
            performance_scores['信息增益'] = min(metrics['information_gain'] / 10.0, 1.0)
        
        # 绘制雷达图
        angles = np.linspace(0, 2*np.pi, len(performance_scores), endpoint=False)
        values = list(performance_scores.values())
        labels = list(performance_scores.keys())
        
        # 闭合图形
        angles = np.concatenate((angles, [angles[0]]))
        values = values + [values[0]]
        
        ax.plot(angles, values, 'o-', linewidth=2, color='#FF6B6B')
        ax.fill(angles, values, alpha=0.25, color='#FF6B6B')
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(labels)
        ax.set_ylim(0, 1)
        ax.set_title('性能评估雷达图')
        ax.grid(True)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='单一边界检测方法测试工具')
    parser.add_argument('--method', '-m', 
                       choices=['information_theory', 'statistical_significance', 
                               'adaptive_threshold', 'bayesian_changepoint'],
                       default='information_theory',
                       help='选择要测试的边界检测方法')
    parser.add_argument('--scenario', '-s',
                       choices=['mixed_normal', 'exponential', 'bimodal', 'noisy_step'],
                       default='mixed_normal',
                       help='选择测试数据场景')
    
    args = parser.parse_args()
    
    # 创建测试器
    tester = SingleMethodTester(args.method)
    
    # 生成测试数据
    test_data = tester.generate_test_data(args.scenario)
    
    # 运行测试
    result = tester.test_method(test_data, visualize=True)
    
    if result:
        print(f"\n🎉 {tester.method_display_names[args.method]} 测试完成!")
        print(f"   场景: {args.scenario}")
        print(f"   边界距离: {result['boundary_distance']:.4f}")
        print(f"   置信度: {result['confidence']:.4f}")
    else:
        print(f"\n❌ {tester.method_display_names[args.method]} 测试失败!")

if __name__ == "__main__":
    main()
