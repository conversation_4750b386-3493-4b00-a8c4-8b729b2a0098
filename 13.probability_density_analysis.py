"""
概率密度分析系统 - 船舶领域拟合第一步
基于机动时刻数据，划分扇区，计算概率密度，找到边界

核心功能：
1. 加载机动时刻数据
2. 提取相对位置数据
3. 划分扇区（8个扇区）
4. 计算概率密度分布
5. 找到各扇区的概率密度边界
6. 可视化扇区划分和边界检测结果

输入：crossing_avoidance_scenes.pkl, overtaking_avoidance_scenes.pkl
输出：sector_boundaries_results.pkl

使用方法：
python probability_density_analysis.py
"""

import pickle
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from tqdm import tqdm
import math
from scipy import stats
from scipy.ndimage import gaussian_filter
from scipy.interpolate import griddata

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False


class ProbabilityDensityAnalyzer:
    """概率密度分析器"""
    
    def __init__(self, data_months=['2024_1', '2024_2', '2024_3'], num_sectors=8, debug=False):
        """
        初始化概率密度分析器
        
        :param data_months: 数据月份列表
        :param num_sectors: 扇区数量
        :param debug: 是否开启调试模式
        """
        self.data_months = data_months if isinstance(data_months, list) else [data_months]
        self.num_sectors = num_sectors
        self.debug = debug
        
        # 船长区间定义 - 重构为3个区间
        self.length_intervals = [
            (0, 100, "100m以下"),
            (100, 200, "100-200m"),
            (200, 500, "200m以上")
        ]
        
        # 扇区角度范围
        self.sector_angles = self._calculate_sector_angles()
        
        # 数据存储
        self.crossing_moments = []
        self.overtaking_moments = []
        self.crossing_relative_data = {}
        self.overtaking_relative_data = {}
        
        # 分析结果
        self.density_results = {}
        self.sector_boundaries = {}
        
        print(f"🔍 概率密度分析器初始化完成")
        print(f"   数据月份: {self.data_months}")
        print(f"   扇区数量: {num_sectors}")
        print(f"   船长区间: {[name for _, _, name in self.length_intervals]}")
    
    def _calculate_sector_angles(self):
        """计算扇区角度范围"""
        sector_width = 2 * np.pi / self.num_sectors
        angles = []
        
        for i in range(self.num_sectors):
            start_angle = i * sector_width - np.pi  # 从-π开始
            end_angle = (i + 1) * sector_width - np.pi
            angles.append((start_angle, end_angle, f"扇区{i+1}"))
        
        return angles
    
    def load_maneuvering_moments(self):
        """加载机动时刻数据"""
        print("\n=== 加载机动时刻数据 ===")
        
        total_crossing = 0
        total_overtaking = 0
        
        for data_month in self.data_months:
            print(f"\n--- 加载 {data_month} 数据 ---")
            
            # 加载交叉避让机动时刻
            crossing_file = f'result/{data_month}/crossing_avoidance_scenes.pkl'
            if Path(crossing_file).exists():
                with open(crossing_file, 'rb') as f:
                    moments = pickle.load(f)
                self.crossing_moments.extend(moments)
                total_crossing += len(moments)
                print(f"   交叉避让: {len(moments)} 个时刻")
            else:
                print(f"⚠️  未找到: {crossing_file}")
            
            # 加载追越避让机动时刻（仅使用最后一个月）
            if data_month == self.data_months[-1]:
                overtaking_file = f'result/{data_month}/overtaking_avoidance_scenes.pkl'
                if Path(overtaking_file).exists():
                    with open(overtaking_file, 'rb') as f:
                        moments = pickle.load(f)
                    self.overtaking_moments.extend(moments)
                    total_overtaking += len(moments)
                    print(f"   追越避让: {len(moments)} 个时刻（仅使用最后月份）")
                else:
                    print(f"⚠️  未找到: {overtaking_file}")
        
        print(f"\n✅ 机动时刻加载完成:")
        print(f"   交叉避让: {total_crossing} 个")
        print(f"   追越避让: {total_overtaking} 个")
    
    def extract_relative_positions(self):
        """提取相对位置数据"""
        print("\n=== 提取相对位置数据 ===")
        
        # 初始化数据结构
        for _, _, interval_name in self.length_intervals:
            self.crossing_relative_data[interval_name] = []
            self.overtaking_relative_data[interval_name] = []
        
        # 处理交叉避让
        print("处理交叉避让机动时刻...")
        crossing_processed = self._process_moments(self.crossing_moments, self.crossing_relative_data)
        
        # 处理追越避让
        print("处理追越避让机动时刻...")
        overtaking_processed = self._process_moments(self.overtaking_moments, self.overtaking_relative_data)
        
        print(f"✅ 相对位置提取完成:")
        print(f"   交叉避让: {crossing_processed} 个有效相对位置")
        print(f"   追越避让: {overtaking_processed} 个有效相对位置")
        
        # 打印统计
        self._print_data_stats()
    
    def _process_moments(self, moments, target_data_dict):
        """处理机动时刻，提取相对位置"""
        processed_count = 0
        
        for moment in tqdm(moments, desc="提取相对位置"):
            try:
                # 提取机动船和目标船
                maneuvering_ship = moment[0]  # 机动船（本船）
                target_ship = moment[1]  # 被避让船（目标船）
                
                # 数据质量检查
                if not self._validate_ship_data(maneuvering_ship, target_ship):
                    continue
                
                # 获取船长区间
                own_length = maneuvering_ship.get('length', 100.0)
                interval_name = self._get_length_interval(own_length)
                
                if interval_name is None:
                    continue
                
                # 计算相对位置
                relative_pos = self._calculate_relative_position(maneuvering_ship, target_ship)
                
                if relative_pos is not None and self._validate_relative_position(relative_pos):
                    target_data_dict[interval_name].append(relative_pos)
                    processed_count += 1
                    
            except Exception as e:
                if self.debug:
                    print(f"处理机动时刻失败: {e}")
                continue
        
        return processed_count
    
    def _validate_ship_data(self, ship1, ship2):
        """验证船舶数据质量"""
        # 检查必要字段
        required_fields = ['mmsi', 'lon', 'lat', 'cog', 'sog', 'length']
        for ship in [ship1, ship2]:
            for field in required_fields:
                if field not in ship or ship[field] is None:
                    return False
        
        # 检查数值合理性
        if (abs(ship1['lon']) > 180 or abs(ship1['lat']) > 90 or
                abs(ship2['lon']) > 180 or abs(ship2['lat']) > 90):
            return False
        
        # 检查船舶尺寸合理性
        if (ship1['length'] <= 0 or ship1['length'] > 500 or
                ship2['length'] <= 0 or ship2['length'] > 500):
            return False
        
        # 检查速度合理性（0-30节）
        if (ship1['sog'] < 0 or ship1['sog'] > 30 or
                ship2['sog'] < 0 or ship2['sog'] > 30):
            return False
        
        return True
    
    def _validate_relative_position(self, relative_pos):
        """验证相对位置的合理性"""
        distance = relative_pos['distance']
        
        # 只检查距离是否为正数
        if distance <= 0:
            return False
        
        return True
    
    def _calculate_relative_position(self, own_ship, target_ship):
        """计算相对位置"""
        try:
            # 经纬度转米（简化计算）
            lat_to_m = 111000
            lon_to_m = 111000 * math.cos(math.radians(own_ship['lat']))
            
            # 计算地理坐标差
            delta_lon = target_ship['lon'] - own_ship['lon']
            delta_lat = target_ship['lat'] - own_ship['lat']
            
            # 转为米制坐标
            x_geo = delta_lon * lon_to_m
            y_geo = delta_lat * lat_to_m
            
            # 转换到本船坐标系
            x_rel, y_rel = self._convert_to_ship_coordinate(x_geo, y_geo, own_ship['cog'])
            
            # 计算距离
            distance = math.sqrt(x_rel ** 2 + y_rel ** 2)
            
            return {
                'relative_x': x_rel,
                'relative_y': y_rel,
                'distance': distance,
                'own_ship_mmsi': own_ship['mmsi'],
                'target_ship_mmsi': target_ship['mmsi'],
                'own_ship_length': own_ship.get('length', 100.0),
                'time_point': own_ship['time_point']
            }
            
        except Exception as e:
            if self.debug:
                print(f"计算相对位置失败: {e}")
            return None
    
    @staticmethod
    def _convert_to_ship_coordinate(x_geo, y_geo, heading):
        """转换到船舶坐标系（船头向前为Y轴正方向）"""
        heading_rad = math.radians(-heading)
        cos_h = math.cos(heading_rad)
        sin_h = math.sin(heading_rad)
        
        x_ship = cos_h * x_geo + sin_h * y_geo
        y_ship = -sin_h * x_geo + cos_h * y_geo
        
        return x_ship, y_ship
    
    def _get_length_interval(self, length):
        """根据船长获取区间名称 - 修复边界判断"""
        # 特殊处理：确保所有合理长度的船舶都能被分类
        if length <= 0 or length > 600:  # 排除不合理的长度
            return None

        for i, (min_len, max_len, interval_name) in enumerate(self.length_intervals):
            if i == len(self.length_intervals) - 1:
                # 最后一个区间：包含上边界及以上所有船舶
                if length >= min_len:
                    return interval_name
            else:
                # 其他区间：左闭右开
                if min_len <= length < max_len:
                    return interval_name

        return None
    
    def _print_data_stats(self):
        """打印数据统计"""
        print(f"\n📊 相对位置数据统计:")
        for _, _, interval_name in self.length_intervals:
            crossing_count = len(self.crossing_relative_data[interval_name])
            overtaking_count = len(self.overtaking_relative_data[interval_name])
            print(f"   {interval_name}: 交叉{crossing_count}个, 追越{overtaking_count}个")

    def analyze_probability_density(self):
        """分析概率密度并找到扇区边界"""
        print("\n=== 概率密度分析与边界检测 ===")

        # 分析交叉避让
        print("\n--- 分析交叉避让场景 ---")
        for _, _, interval_name in self.length_intervals:
            relative_data = self.crossing_relative_data[interval_name]

            if len(relative_data) >= 20:
                self._analyze_scenario_density("交叉", interval_name, relative_data)

        # 分析追越避让
        print("\n--- 分析追越避让场景 ---")
        for _, _, interval_name in self.length_intervals:
            relative_data = self.overtaking_relative_data[interval_name]

            if len(relative_data) >= 10:
                self._analyze_scenario_density("追越", interval_name, relative_data)

        print("✅ 概率密度分析与边界检测完成")

    def _analyze_scenario_density(self, scenario_type, length_interval, relative_data):
        """分析单个场景的概率密度"""
        print(f"📊 分析 {scenario_type}-{length_interval} 的概率密度...")

        # 提取坐标
        x_coords = np.array([p['relative_x'] for p in relative_data])
        y_coords = np.array([p['relative_y'] for p in relative_data])

        # 计算2D概率密度
        density_result = self._calculate_2d_density(x_coords, y_coords)

        if density_result is None:
            print(f"⚠️  {scenario_type}-{length_interval}: 密度计算失败")
            return

        # 按扇区分析
        sector_analysis = self._analyze_by_sectors(x_coords, y_coords, density_result, scenario_type)

        # 存储结果
        key = f"{scenario_type}_{length_interval}"
        self.density_results[key] = {
            'x_coords': x_coords,
            'y_coords': y_coords,
            'density_result': density_result,
            'sector_analysis': sector_analysis,
            'data_count': len(relative_data),
            'scenario_type': scenario_type,
            'length_interval': length_interval
        }

        # 提取扇区边界
        boundaries = self._extract_sector_boundaries(sector_analysis, scenario_type)
        if boundaries:
            self.sector_boundaries[key] = boundaries
            print(f"✅ {scenario_type}-{length_interval}: 找到{len(boundaries)}个扇区边界")
        else:
            print(f"⚠️  {scenario_type}-{length_interval}: 未找到有效边界")

    def _calculate_2d_density(self, x_coords, y_coords, grid_size=50):
        """计算2D概率密度"""
        try:
            # 确定网格范围
            x_min, x_max = np.min(x_coords), np.max(x_coords)
            y_min, y_max = np.min(y_coords), np.max(y_coords)

            # 扩展边界
            x_range = x_max - x_min
            y_range = y_max - y_min
            x_min -= x_range * 0.1
            x_max += x_range * 0.1
            y_min -= y_range * 0.1
            y_max += y_range * 0.1

            # 创建网格
            x_grid = np.linspace(x_min, x_max, grid_size)
            y_grid = np.linspace(y_min, y_max, grid_size)
            X, Y = np.meshgrid(x_grid, y_grid)

            # 计算核密度估计
            positions = np.vstack([X.ravel(), Y.ravel()])
            values = np.vstack([x_coords, y_coords])

            # 使用高斯核密度估计
            kernel = stats.gaussian_kde(values)
            density = np.reshape(kernel(positions).T, X.shape)

            # 高斯平滑
            density_smoothed = gaussian_filter(density, sigma=1.0)

            return {
                'X': X,
                'Y': Y,
                'density': density_smoothed,
                'x_grid': x_grid,
                'y_grid': y_grid,
                'x_range': (x_min, x_max),
                'y_range': (y_min, y_max)
            }

        except Exception as e:
            if self.debug:
                print(f"2D密度计算失败: {e}")
            return None

    def _analyze_by_sectors(self, x_coords, y_coords, density_result, scenario_type):
        """改进的自适应扇区分析"""
        # 1. 根据数据分布自适应调整扇区
        effective_sectors = self._get_adaptive_sectors(x_coords, y_coords, scenario_type)

        sector_results = {}

        for sector_info in effective_sectors:
            start_angle = sector_info['start_angle']
            end_angle = sector_info['end_angle']
            sector_name = sector_info['name']

            # 选择该扇区内的点
            angles = np.arctan2(y_coords, x_coords)

            # 处理角度跨越边界的情况
            if start_angle < end_angle:
                sector_mask = (angles >= start_angle) & (angles < end_angle)
            else:  # 跨越-π/π边界
                sector_mask = (angles >= start_angle) | (angles < end_angle)

            sector_x = x_coords[sector_mask]
            sector_y = y_coords[sector_mask]

            # 追越避让的特殊过滤
            if scenario_type == "追越":
                # 保留前方和侧前方的点（Y > -50米，允许少量侧后方数据）
                forward_mask = sector_y > -50  # 更宽松的过滤条件
                sector_x = sector_x[forward_mask]
                sector_y = sector_y[forward_mask]

                if self.debug:
                    print(f"   {sector_name}: 过滤后点数 {len(sector_x)}")

            # 数据质量检查
            if not self._validate_sector_data_quality(sector_x, sector_y, scenario_type):
                sector_results[sector_name] = None
                continue

            # 计算该扇区的边界
            boundary_info = self._find_sector_boundary(
                sector_x, sector_y, density_result, start_angle, end_angle, scenario_type
            )

            sector_results[sector_name] = {
                'x_coords': sector_x,
                'y_coords': sector_y,
                'point_count': len(sector_x),
                'angle_range': (start_angle, end_angle),
                'boundary_info': boundary_info,
                'data_quality': self._assess_sector_data_quality(sector_x, sector_y)
            }

        return sector_results

    def _get_adaptive_sectors(self, x_coords, y_coords, scenario_type):
        """根据数据分布自适应调整扇区"""
        try:
            angles = np.arctan2(y_coords, x_coords)

            if scenario_type == "追越":
                # 追越：重点关注前方和侧前方
                # 过滤出前方数据（Y > -100米，允许一些侧后方）
                forward_mask = y_coords > -100
                forward_angles = angles[forward_mask]

                if len(forward_angles) < 10:
                    return []  # 前方数据太少

                # 根据前方数据分布调整扇区
                angle_min, angle_max = np.min(forward_angles), np.max(forward_angles)
                angle_range = angle_max - angle_min

                # 如果角度范围太小，扩展一些
                if angle_range < np.pi/2:
                    center_angle = (angle_min + angle_max) / 2
                    angle_min = center_angle - np.pi/4
                    angle_max = center_angle + np.pi/4

                # 创建4-6个前方扇区
                num_forward_sectors = min(6, max(4, len(forward_angles) // 20))
                sector_width = (angle_max - angle_min) / num_forward_sectors

                sectors = []
                for i in range(num_forward_sectors):
                    start_angle = angle_min + i * sector_width
                    end_angle = angle_min + (i + 1) * sector_width
                    sectors.append({
                        'start_angle': start_angle,
                        'end_angle': end_angle,
                        'name': f"前方扇区{i+1}"
                    })

                return sectors

            else:
                # 交叉：使用标准8扇区，但根据数据密度调整
                sectors = []
                for i, (start_angle, end_angle, sector_name) in enumerate(self.sector_angles):
                    # 检查该扇区是否有足够数据
                    if start_angle < end_angle:
                        sector_mask = (angles >= start_angle) & (angles < end_angle)
                    else:
                        sector_mask = (angles >= start_angle) | (angles < end_angle)

                    sector_point_count = np.sum(sector_mask)

                    # 如果数据点太少，考虑与相邻扇区合并
                    if sector_point_count >= 5:  # 最小数据点要求
                        sectors.append({
                            'start_angle': start_angle,
                            'end_angle': end_angle,
                            'name': sector_name
                        })

                return sectors

        except Exception as e:
            if self.debug:
                print(f"自适应扇区调整失败: {e}")
            # 回退到标准扇区
            return [{'start_angle': s[0], 'end_angle': s[1], 'name': s[2]}
                   for s in self.sector_angles]

    def _validate_sector_data_quality(self, sector_x, sector_y, scenario_type):
        """验证扇区数据质量"""
        min_points = 3 if scenario_type == "追越" else 5

        if len(sector_x) < min_points:
            return False

        # 检查数据分布是否合理
        distances = np.sqrt(sector_x**2 + sector_y**2)

        # 距离范围检查
        if np.max(distances) - np.min(distances) < 20:  # 距离变化太小
            return False

        # 异常值比例检查
        Q1, Q3 = np.percentile(distances, [25, 75])
        IQR = Q3 - Q1
        outlier_mask = (distances < Q1 - 1.5*IQR) | (distances > Q3 + 1.5*IQR)
        outlier_ratio = np.sum(outlier_mask) / len(distances)

        if outlier_ratio > 0.3:  # 异常值太多
            return False

        return True

    def _assess_sector_data_quality(self, sector_x, sector_y):
        """评估扇区数据质量"""
        try:
            distances = np.sqrt(sector_x**2 + sector_y**2)

            quality_score = 0.0

            # 1. 数据点数量 (40%)
            point_count_score = min(1.0, len(distances) / 20.0)
            quality_score += point_count_score * 0.4

            # 2. 数据分布均匀性 (30%)
            if len(distances) > 1:
                cv = np.std(distances) / np.mean(distances)  # 变异系数
                uniformity_score = max(0, 1.0 - cv)
                quality_score += uniformity_score * 0.3

            # 3. 距离范围合理性 (30%)
            distance_range = np.max(distances) - np.min(distances)
            range_score = min(1.0, distance_range / 200.0)  # 200米为理想范围
            quality_score += range_score * 0.3

            return {
                'overall_score': quality_score,
                'point_count': len(distances),
                'distance_range': distance_range,
                'mean_distance': np.mean(distances),
                'std_distance': np.std(distances)
            }

        except Exception as e:
            if self.debug:
                print(f"数据质量评估失败: {e}")
            return {'overall_score': 0.0}

    def _find_sector_boundary(self, sector_x, sector_y, density_result, start_angle, end_angle, scenario_type):
        """找到扇区的概率密度边界"""
        try:
            if len(sector_x) < 5:
                return None

            # 计算距离统计信息
            distances = np.sqrt(sector_x**2 + sector_y**2)

            if scenario_type == "交叉":
                # 交叉避让使用密度梯度方法
                density_boundary = self._find_density_boundary_in_sector(
                    sector_x, sector_y, density_result
                )

                # 同时计算分位数作为备选
                percentiles = [50, 70, 85, 95]
                percentile_boundaries = {}
                for p in percentiles:
                    percentile_boundaries[f"p{p}"] = np.percentile(distances, p)

                return {
                    'density_boundary': density_boundary,
                    'percentile_boundaries': percentile_boundaries,
                    'mean_distance': np.mean(distances),
                    'std_distance': np.std(distances),
                    'max_distance': np.max(distances),
                    'method': 'density_gradient_primary',
                    'scenario_type': scenario_type
                }

            else:  # 追越避让
                # 追越避让使用分位数方法
                percentiles = [50, 70, 85, 95]
                percentile_boundaries = {}
                for p in percentiles:
                    percentile_boundaries[f"p{p}"] = np.percentile(distances, p)

                # 也尝试密度梯度方法作为参考
                density_boundary = self._find_density_boundary_in_sector(
                    sector_x, sector_y, density_result
                )

                return {
                    'percentile_boundaries': percentile_boundaries,
                    'density_boundary': density_boundary,
                    'mean_distance': np.mean(distances),
                    'std_distance': np.std(distances),
                    'max_distance': np.max(distances),
                    'method': 'percentile_primary',
                    'scenario_type': scenario_type
                }

        except Exception as e:
            if self.debug:
                print(f"扇区边界计算失败: {e}")
            return None

    def _find_density_boundary_in_sector(self, sector_x, sector_y, density_result):
        """改进的扇区密度边界检测"""
        try:
            # 1. 数据预处理：异常值检测和清理
            cleaned_x, cleaned_y = self._clean_sector_data(sector_x, sector_y)

            if len(cleaned_x) < 5:
                return None

            # 2. 多方向采样而非单一平均角度
            boundary_candidates = []

            # 计算扇区角度范围
            angles = np.arctan2(cleaned_y, cleaned_x)
            angle_min, angle_max = np.min(angles), np.max(angles)

            # 在扇区内采样多个方向
            num_directions = min(5, len(cleaned_x) // 2)
            sample_angles = np.linspace(angle_min, angle_max, num_directions)

            for sample_angle in sample_angles:
                boundary_dist = self._find_boundary_along_direction(
                    cleaned_x, cleaned_y, sample_angle, density_result
                )
                if boundary_dist is not None:
                    boundary_candidates.append({
                        'distance': boundary_dist,
                        'angle': sample_angle,
                        'confidence': self._calculate_boundary_confidence(
                            cleaned_x, cleaned_y, boundary_dist, sample_angle
                        )
                    })

            # 3. 选择最可靠的边界
            if boundary_candidates:
                best_boundary = max(boundary_candidates, key=lambda x: x['confidence'])
                return {
                    'boundary_distance': best_boundary['distance'],
                    'boundary_angle': best_boundary['angle'],
                    'confidence': best_boundary['confidence'],
                    'method': 'improved_multi_direction',
                    'candidates_count': len(boundary_candidates)
                }

            return None

        except Exception as e:
            if self.debug:
                print(f"改进密度边界检测失败: {e}")
            return None

    def _clean_sector_data(self, sector_x, sector_y):
        """清理扇区数据，移除异常值"""
        try:
            # 计算距离
            distances = np.sqrt(sector_x**2 + sector_y**2)

            # 使用IQR方法检测异常值
            Q1 = np.percentile(distances, 25)
            Q3 = np.percentile(distances, 75)
            IQR = Q3 - Q1

            # 定义异常值边界（更宽松的1.5倍IQR）
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR

            # 过滤异常值
            valid_mask = (distances >= max(lower_bound, 10)) & (distances <= min(upper_bound, 2000))

            return sector_x[valid_mask], sector_y[valid_mask]

        except Exception as e:
            if self.debug:
                print(f"数据清理失败: {e}")
            return sector_x, sector_y

    def _find_boundary_along_direction(self, sector_x, sector_y, direction_angle, density_result):
        """沿指定方向寻找边界"""
        try:
            # 计算该方向上的最大距离
            distances = np.sqrt(sector_x**2 + sector_y**2)
            max_distance = np.max(distances)

            # 沿方向采样
            sample_distances = np.linspace(10, max_distance * 1.1, 50)  # 从10米开始
            sample_x = sample_distances * np.cos(direction_angle)
            sample_y = sample_distances * np.sin(direction_angle)

            # 改进的密度插值：使用最近邻+线性插值组合
            sample_density = self._robust_density_interpolation(
                sample_x, sample_y, density_result
            )

            if sample_density is None or len(sample_density) < 10:
                return None

            # 平滑密度曲线
            from scipy.ndimage import gaussian_filter1d
            smoothed_density = gaussian_filter1d(sample_density, sigma=1.5)

            # 改进的边界检测：多种策略组合
            boundary_distance = self._detect_boundary_multi_strategy(
                sample_distances, smoothed_density
            )

            return boundary_distance

        except Exception as e:
            if self.debug:
                print(f"方向边界检测失败: {e}")
            return None

    def _robust_density_interpolation(self, sample_x, sample_y, density_result):
        """鲁棒的密度插值"""
        try:
            X, Y = density_result['X'], density_result['Y']
            density = density_result['density']

            # 将网格点展平
            grid_points = np.column_stack([X.ravel(), Y.ravel()])
            density_values = density.ravel()

            # 移除无效值
            valid_mask = ~np.isnan(density_values) & (density_values >= 0)
            grid_points = grid_points[valid_mask]
            density_values = density_values[valid_mask]

            if len(density_values) < 10:
                return None

            # 组合插值：先最近邻，再线性插值
            sample_points = np.column_stack([sample_x, sample_y])

            # 最近邻插值作为基础
            from scipy.interpolate import griddata
            nearest_density = griddata(
                grid_points, density_values, sample_points,
                method='nearest', fill_value=0
            )

            # 线性插值进行精细化
            try:
                linear_density = griddata(
                    grid_points, density_values, sample_points,
                    method='linear', fill_value=np.nan
                )

                # 组合结果：优先使用线性插值，缺失处用最近邻
                combined_density = np.where(
                    np.isnan(linear_density), nearest_density, linear_density
                )

                return combined_density

            except Exception:
                # 如果线性插值失败，使用最近邻结果
                return nearest_density

        except Exception as e:
            if self.debug:
                print(f"密度插值失败: {e}")
            return None

    def _detect_boundary_multi_strategy(self, sample_distances, smoothed_density):
        """基于统计理论的边界检测方法"""
        try:
            from scipy import stats
            from scipy.signal import find_peaks
            from scipy.optimize import minimize_scalar

            boundary_candidates = []

            # 方法1: 基于信息论的变点检测 (Change Point Detection)
            boundary_info_theory = self._detect_boundary_information_theory(
                sample_distances, smoothed_density
            )
            if boundary_info_theory is not None:
                boundary_candidates.append({
                    'distance': boundary_info_theory['distance'],
                    'method': 'information_theory',
                    'score': boundary_info_theory['score'],
                    'confidence': boundary_info_theory['confidence']
                })

            # 方法2: 基于统计显著性的边界检测
            boundary_statistical = self._detect_boundary_statistical_significance(
                sample_distances, smoothed_density
            )
            if boundary_statistical is not None:
                boundary_candidates.append({
                    'distance': boundary_statistical['distance'],
                    'method': 'statistical_significance',
                    'score': boundary_statistical['score'],
                    'confidence': boundary_statistical['confidence']
                })

            # 方法3: 基于核密度估计的自适应阈值
            boundary_adaptive = self._detect_boundary_adaptive_threshold(
                sample_distances, smoothed_density
            )
            if boundary_adaptive is not None:
                boundary_candidates.append({
                    'distance': boundary_adaptive['distance'],
                    'method': 'adaptive_threshold',
                    'score': boundary_adaptive['score'],
                    'confidence': boundary_adaptive['confidence']
                })

            # 方法4: 基于贝叶斯变点检测
            boundary_bayesian = self._detect_boundary_bayesian(
                sample_distances, smoothed_density
            )
            if boundary_bayesian is not None:
                boundary_candidates.append({
                    'distance': boundary_bayesian['distance'],
                    'method': 'bayesian_changepoint',
                    'score': boundary_bayesian['score'],
                    'confidence': boundary_bayesian['confidence']
                })

            # 使用加权投票选择最佳边界
            if boundary_candidates:
                return self._select_best_boundary_weighted(boundary_candidates)

            return None

        except Exception as e:
            if self.debug:
                print(f"统计边界检测失败: {e}")
            return None

    def _detect_boundary_information_theory(self, sample_distances, smoothed_density):
        """基于信息论的变点检测 - 使用Kullback-Leibler散度"""
        try:
            from scipy.stats import entropy

            n = len(smoothed_density)
            if n < 10:
                return None

            max_kl_div = 0
            best_changepoint = None

            # 在中间60%区域寻找变点，避免边界效应
            start_idx = int(n * 0.2)
            end_idx = int(n * 0.8)

            for i in range(start_idx, end_idx):
                # 分割密度分布为两部分
                left_density = smoothed_density[:i+1]
                right_density = smoothed_density[i+1:]

                if len(left_density) < 3 or len(right_density) < 3:
                    continue

                # 归一化为概率分布
                left_prob = left_density / np.sum(left_density)
                right_prob = right_density / np.sum(right_density)

                # 计算整体分布
                overall_prob = smoothed_density / np.sum(smoothed_density)
                left_overall = overall_prob[:i+1]
                right_overall = overall_prob[i+1:]

                # 计算KL散度
                kl_left = entropy(left_prob, left_overall) if len(left_prob) == len(left_overall) else 0
                kl_right = entropy(right_prob, right_overall) if len(right_prob) == len(right_overall) else 0

                # 加权KL散度
                total_kl = (len(left_density) * kl_left + len(right_density) * kl_right) / n

                if total_kl > max_kl_div:
                    max_kl_div = total_kl
                    best_changepoint = i

            if best_changepoint is not None and max_kl_div > 0.1:  # 显著性阈值
                confidence = min(max_kl_div / 2.0, 1.0)  # 转换为置信度
                return {
                    'distance': sample_distances[best_changepoint],
                    'score': max_kl_div,
                    'confidence': confidence
                }

            return None

        except Exception as e:
            if self.debug:
                print(f"信息论边界检测失败: {e}")
            return None

    def _detect_boundary_statistical_significance(self, sample_distances, smoothed_density):
        """基于统计显著性的边界检测 - 使用t检验"""
        try:
            from scipy.stats import ttest_ind

            n = len(smoothed_density)
            if n < 20:
                return None

            max_t_stat = 0
            best_boundary = None
            best_p_value = 1.0

            # 在中间60%区域寻找显著差异点
            start_idx = int(n * 0.2)
            end_idx = int(n * 0.8)

            for i in range(start_idx, end_idx):
                # 分割为两个样本
                left_sample = smoothed_density[:i+1]
                right_sample = smoothed_density[i+1:]

                if len(left_sample) < 5 or len(right_sample) < 5:
                    continue

                # 进行t检验
                t_stat, p_value = ttest_ind(left_sample, right_sample)

                # 寻找最大t统计量（最显著差异）
                if abs(t_stat) > max_t_stat and p_value < 0.05:  # 显著性水平
                    max_t_stat = abs(t_stat)
                    best_boundary = i
                    best_p_value = p_value

            if best_boundary is not None:
                confidence = 1 - best_p_value  # p值越小，置信度越高
                return {
                    'distance': sample_distances[best_boundary],
                    'score': max_t_stat,
                    'confidence': confidence
                }

            return None

        except Exception as e:
            if self.debug:
                print(f"统计显著性边界检测失败: {e}")
            return None

    def _detect_boundary_adaptive_threshold(self, sample_distances, smoothed_density):
        """基于核密度估计的自适应阈值方法 - Otsu方法的连续版本"""
        try:
            # 使用Otsu方法的思想：最大化类间方差
            n = len(smoothed_density)
            if n < 10:
                return None

            max_variance_ratio = 0
            best_threshold_idx = None

            # 计算总体均值和方差
            total_mean = np.average(range(n), weights=smoothed_density)
            total_variance = np.average((np.arange(n) - total_mean)**2, weights=smoothed_density)

            if total_variance == 0:
                return None

            # 在中间80%区域寻找最优阈值
            start_idx = int(n * 0.1)
            end_idx = int(n * 0.9)

            for t in range(start_idx, end_idx):
                # 计算两个类的权重
                w1 = np.sum(smoothed_density[:t+1])
                w2 = np.sum(smoothed_density[t+1:])

                if w1 == 0 or w2 == 0:
                    continue

                # 计算两个类的均值
                mu1 = np.average(range(t+1), weights=smoothed_density[:t+1])
                mu2 = np.average(range(t+1, n), weights=smoothed_density[t+1:])

                # 计算类间方差
                between_class_variance = w1 * w2 * (mu1 - mu2)**2
                variance_ratio = between_class_variance / total_variance

                if variance_ratio > max_variance_ratio:
                    max_variance_ratio = variance_ratio
                    best_threshold_idx = t

            if best_threshold_idx is not None and max_variance_ratio > 0.1:
                confidence = min(max_variance_ratio, 1.0)
                return {
                    'distance': sample_distances[best_threshold_idx],
                    'score': max_variance_ratio,
                    'confidence': confidence
                }

            return None

        except Exception as e:
            if self.debug:
                print(f"自适应阈值边界检测失败: {e}")
            return None

    def _detect_boundary_bayesian(self, sample_distances, smoothed_density):
        """基于贝叶斯变点检测"""
        try:
            # 简化的贝叶斯变点检测：使用似然比
            n = len(smoothed_density)
            if n < 15:
                return None

            max_likelihood_ratio = 0
            best_changepoint = None

            # 在中间70%区域寻找变点
            start_idx = int(n * 0.15)
            end_idx = int(n * 0.85)

            for cp in range(start_idx, end_idx):
                # 分割数据
                left_data = smoothed_density[:cp+1]
                right_data = smoothed_density[cp+1:]

                if len(left_data) < 5 or len(right_data) < 5:
                    continue

                # 计算各段的参数（假设正态分布）
                left_mean, left_std = np.mean(left_data), np.std(left_data)
                right_mean, right_std = np.mean(right_data), np.std(right_data)
                overall_mean, overall_std = np.mean(smoothed_density), np.std(smoothed_density)

                if left_std == 0 or right_std == 0 or overall_std == 0:
                    continue

                # 计算对数似然比
                # H1: 有变点 vs H0: 无变点
                log_likelihood_h1 = (
                    -0.5 * len(left_data) * np.log(2 * np.pi * left_std**2) -
                    np.sum((left_data - left_mean)**2) / (2 * left_std**2) -
                    0.5 * len(right_data) * np.log(2 * np.pi * right_std**2) -
                    np.sum((right_data - right_mean)**2) / (2 * right_std**2)
                )

                log_likelihood_h0 = (
                    -0.5 * n * np.log(2 * np.pi * overall_std**2) -
                    np.sum((smoothed_density - overall_mean)**2) / (2 * overall_std**2)
                )

                likelihood_ratio = log_likelihood_h1 - log_likelihood_h0

                if likelihood_ratio > max_likelihood_ratio:
                    max_likelihood_ratio = likelihood_ratio
                    best_changepoint = cp

            if best_changepoint is not None and max_likelihood_ratio > 2.0:  # 显著性阈值
                # 使用似然比计算置信度
                confidence = min(1.0 / (1.0 + np.exp(-max_likelihood_ratio/5.0)), 1.0)
                return {
                    'distance': sample_distances[best_changepoint],
                    'score': max_likelihood_ratio,
                    'confidence': confidence
                }

            return None

        except Exception as e:
            if self.debug:
                print(f"贝叶斯边界检测失败: {e}")
            return None

    def _select_best_boundary_weighted(self, boundary_candidates):
        """使用加权投票选择最佳边界"""
        try:
            if not boundary_candidates:
                return None

            # 方法权重（基于理论可靠性）
            method_weights = {
                'information_theory': 0.3,      # 信息论方法权重最高
                'bayesian_changepoint': 0.25,   # 贝叶斯方法
                'statistical_significance': 0.25, # 统计显著性
                'adaptive_threshold': 0.2       # 自适应阈值
            }

            # 计算加权分数
            weighted_candidates = []
            for candidate in boundary_candidates:
                method = candidate['method']
                weight = method_weights.get(method, 0.1)

                # 综合分数 = 方法权重 × 置信度 × 标准化分数
                normalized_score = min(candidate['score'] / 10.0, 1.0)  # 标准化到[0,1]
                weighted_score = weight * candidate['confidence'] * normalized_score

                weighted_candidates.append({
                    'distance': candidate['distance'],
                    'weighted_score': weighted_score,
                    'method': method,
                    'confidence': candidate['confidence']
                })

            # 选择加权分数最高的候选
            best_candidate = max(weighted_candidates, key=lambda x: x['weighted_score'])

            if self.debug:
                print(f"选择边界: {best_candidate['method']}, "
                      f"距离: {best_candidate['distance']:.3f}, "
                      f"置信度: {best_candidate['confidence']:.3f}")

            return best_candidate['distance']

        except Exception as e:
            if self.debug:
                print(f"加权选择失败: {e}")
            return boundary_candidates[0]['distance'] if boundary_candidates else None

    def _calculate_boundary_confidence(self, sector_x, sector_y, boundary_distance, boundary_angle):
        """计算边界置信度"""
        try:
            # 计算该方向上实际数据点的分布
            distances = np.sqrt(sector_x**2 + sector_y**2)
            angles = np.arctan2(sector_y, sector_x)

            # 找到该方向附近的点（角度容差±15度）
            angle_tolerance = np.radians(15)
            angle_diff = np.abs(angles - boundary_angle)
            angle_diff = np.minimum(angle_diff, 2*np.pi - angle_diff)  # 处理角度环绕

            nearby_mask = angle_diff <= angle_tolerance
            nearby_distances = distances[nearby_mask]

            if len(nearby_distances) < 3:
                return 0.1  # 数据太少，置信度很低

            # 计算置信度指标
            # 1. 数据点数量（更多点 = 更高置信度）
            point_count_score = min(1.0, len(nearby_distances) / 10.0)

            # 2. 边界距离与数据分布的一致性
            median_distance = np.median(nearby_distances)
            distance_consistency = 1.0 - min(1.0, abs(boundary_distance - median_distance) / median_distance)

            # 3. 数据分布的紧密性（标准差越小越好）
            std_distance = np.std(nearby_distances)
            compactness_score = 1.0 / (1.0 + std_distance / median_distance)

            # 综合置信度
            confidence = (point_count_score * 0.4 +
                         distance_consistency * 0.4 +
                         compactness_score * 0.2)

            return confidence

        except Exception as e:
            if self.debug:
                print(f"置信度计算失败: {e}")
            return 0.1

    def _extract_sector_boundaries(self, sector_analysis, scenario_type):
        """提取扇区边界信息 - 追越仅使用前方扇区"""
        boundaries = {}

        for sector_name, sector_data in sector_analysis.items():
            if sector_data is not None and sector_data['boundary_info'] is not None:
                boundary_info = sector_data['boundary_info']

                # 追越避让额外检查：确保边界点在前方（Y > 0）
                if scenario_type == "追越":
                    # 检查扇区中的点是否主要在前方
                    sector_y = sector_data['y_coords']
                    forward_points = np.sum(sector_y > 0)
                    total_points = len(sector_y)

                    if forward_points < total_points * 0.8:  # 如果前方点少于80%，跳过
                        print(f"   跳过扇区 {sector_name}: 前方点比例过低 ({forward_points}/{total_points})")
                        continue

                if scenario_type == "交叉":
                    # 交叉避让优先使用密度梯度边界
                    if (boundary_info['density_boundary'] is not None and
                        'boundary_distance' in boundary_info['density_boundary']):

                        density_boundary = boundary_info['density_boundary']
                        boundaries[sector_name] = {
                            'boundary_distance': density_boundary['boundary_distance'],
                            'boundary_angle': density_boundary['boundary_angle'],
                            'angle_range': sector_data['angle_range'],
                            'point_count': sector_data['point_count'],
                            'method': 'density_gradient'
                        }

                    # 如果密度梯度失败，使用85%分位数作为备选
                    elif 'percentile_boundaries' in boundary_info:
                        angle_range = sector_data['angle_range']
                        center_angle = (angle_range[0] + angle_range[1]) / 2
                        boundaries[sector_name] = {
                            'boundary_distance': boundary_info['percentile_boundaries']['p85'],
                            'boundary_angle': center_angle,
                            'angle_range': angle_range,
                            'point_count': sector_data['point_count'],
                            'method': 'percentile_fallback'
                        }

                else:  # 追越避让
                    # 追越避让优先使用分位数方法（仅前方扇区）
                    if 'percentile_boundaries' in boundary_info:
                        angle_range = sector_data['angle_range']
                        center_angle = (angle_range[0] + angle_range[1]) / 2

                        # 确保边界角度对应前方位置
                        boundary_y = np.sin(center_angle)
                        if boundary_y > 0:  # 只有前方的边界才被接受
                            boundaries[sector_name] = {
                                'boundary_distance': boundary_info['percentile_boundaries']['p85'],
                                'boundary_angle': center_angle,
                                'angle_range': angle_range,
                                'point_count': sector_data['point_count'],
                                'method': 'percentile_forward_only'
                            }
                        else:
                            print(f"   跳过后方边界: {sector_name} (角度: {np.degrees(center_angle):.1f}°)")

        # 追越避让的特殊处理：如果前方边界点太少，尝试补充
        if scenario_type == "追越" and len(boundaries) < 2:
            print(f"   ⚠️  追越避让前方边界点不足({len(boundaries)}个)，尝试放宽条件...")
            # 可以在这里添加更宽松的条件来获取更多边界点

        return boundaries

    def visualize_results(self):
        """可视化概率密度分析结果"""
        print("\n=== 生成可视化结果 ===")

        vis_dir = Path("vis/test/probability_density")
        vis_dir.mkdir(parents=True, exist_ok=True)

        for key, result in self.density_results.items():
            scenario_type = result['scenario_type']
            length_interval = result['length_interval']

            # 创建综合分析图
            self._create_comprehensive_plot(result, key, vis_dir)

        print(f"✅ 可视化结果保存至: {vis_dir}")

    def _create_comprehensive_plot(self, result, key, vis_dir):
        """创建综合分析图"""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle(f'{result["scenario_type"]}避让 - {result["length_interval"]} 概率密度分析',
                    fontsize=16, fontweight='bold')

        # 1. 原始数据散点图
        ax1 = axes[0, 0]
        ax1.scatter(result['x_coords'], result['y_coords'], alpha=0.5, s=1, c='steelblue')
        ax1.set_title(f'原始数据分布 (n={len(result["x_coords"])})')
        ax1.set_xlabel('横向距离 (m)')
        ax1.set_ylabel('纵向距离 (m)')
        ax1.grid(True, alpha=0.3)
        ax1.axis('equal')

        # 2. 概率密度热力图
        ax2 = axes[0, 1]
        density_data = result['density_result']
        im = ax2.contourf(density_data['X'], density_data['Y'],
                         density_data['density'], levels=20, cmap='viridis')
        plt.colorbar(im, ax=ax2, label='概率密度')
        ax2.set_title('概率密度分布')
        ax2.set_xlabel('横向距离 (m)')
        ax2.set_ylabel('纵向距离 (m)')

        # 3. 扇区划分
        ax3 = axes[1, 0]
        self._plot_sectors(ax3, result)

        # 4. 扇区边界
        ax4 = axes[1, 1]
        self._plot_boundaries(ax4, result, key)

        plt.tight_layout()

        # 保存图片
        filename = f"{result['scenario_type']}_{result['length_interval']}_density_analysis.png"
        save_path = vis_dir / filename
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"📊 {key} 分析图保存至: {save_path}")

    def _plot_sectors(self, ax, result):
        """绘制扇区划分"""
        sector_analysis = result['sector_analysis']
        colors = plt.cm.Set3(np.linspace(0, 1, self.num_sectors))

        for i, (sector_name, sector_data) in enumerate(sector_analysis.items()):
            if sector_data is not None:
                ax.scatter(sector_data['x_coords'], sector_data['y_coords'],
                          c=[colors[i]], alpha=0.6, s=2, label=f"{sector_name}({sector_data['point_count']})")

        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)
        ax.set_title(f'扇区划分 ({self.num_sectors}个扇区)')
        ax.set_xlabel('横向距离 (m)')
        ax.set_ylabel('纵向距离 (m)')
        ax.grid(True, alpha=0.3)
        ax.axis('equal')

    def _plot_boundaries(self, ax, result, key):
        """绘制扇区边界"""
        # 绘制原始数据
        ax.scatter(result['x_coords'], result['y_coords'],
                  alpha=0.3, s=1, c='lightgray', label='原始数据')

        # 绘制边界
        if key in self.sector_boundaries:
            boundaries = self.sector_boundaries[key]
            boundary_count = 0

            for sector_name, boundary_info in boundaries.items():
                distance = boundary_info['boundary_distance']
                angle = boundary_info['boundary_angle']
                method = boundary_info['method']

                # 边界点坐标
                bx = distance * np.cos(angle)
                by = distance * np.sin(angle)

                # 根据方法选择颜色
                color = 'red' if 'density' in method else 'blue'

                ax.plot(bx, by, 'o', color=color, markersize=8, alpha=0.8)
                ax.text(bx*1.1, by*1.1,
                       f'{sector_name}\n{distance:.0f}m\n{method[:8]}',
                       ha='center', va='center', fontsize=7,
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7))

                boundary_count += 1

        ax.set_title(f'扇区边界检测 (找到{boundary_count}个边界)')
        ax.set_xlabel('横向距离 (m)')
        ax.set_ylabel('纵向距离 (m)')
        ax.grid(True, alpha=0.3)
        ax.axis('equal')

    def save_results(self):
        """保存分析结果"""
        print("\n=== 保存分析结果 ===")

        result_dir = Path("result/probability_density")
        result_dir.mkdir(parents=True, exist_ok=True)

        # 保存主结果
        results = {
            'density_results': self.density_results,
            'sector_boundaries': self.sector_boundaries,
            'length_intervals': self.length_intervals,
            'num_sectors': self.num_sectors,
            'data_months': self.data_months
        }

        result_file = result_dir / "sector_boundaries_results.pkl"
        with open(result_file, 'wb') as f:
            pickle.dump(results, f)

        print(f"✅ 扇区边界结果保存至: {result_file}")

        return result_file

    def run_full_analysis(self):
        """运行完整的概率密度分析"""
        print("🔍 开始概率密度分析...")
        print("=" * 60)

        try:
            # 执行分析流程
            self.load_maneuvering_moments()
            self.extract_relative_positions()
            self.analyze_probability_density()
            self.visualize_results()
            result_file = self.save_results()

            print("\n" + "=" * 60)
            print("🎉 概率密度分析完成！")
            print(f"📁 主要输出:")
            print(f"   扇区边界结果: {result_file}")
            print(f"   可视化图片: vis/probability_density/")
            print("=" * 60)

            return True

        except Exception as e:
            print(f"\n❌ 概率密度分析失败: {e}")
            if self.debug:
                import traceback
                traceback.print_exc()
            return False


def main():
    """主函数"""
    data_months = ['2024_1', '2024_2', '2024_3']

    print(f"🔍 概率密度分析系统")
    print(f"   数据月份: {data_months}")
    print(f"   扇区数量: 8")
    print(f"   输入文件: crossing_avoidance_scenes.pkl, overtaking_avoidance_scenes.pkl")
    print(f"   输出文件: sector_boundaries_results.pkl")

    # 创建分析器
    analyzer = ProbabilityDensityAnalyzer(
        data_months=data_months,
        num_sectors=36,
        debug=False
    )

    # 运行完整分析
    success = analyzer.run_full_analysis()

    if success:
        print(f"\n📁 主要输出文件:")
        print(f"   扇区边界: result/probability_density/sector_boundaries_results.pkl")
        print(f"   可视化图: vis/probability_density/")
    else:
        print(f"\n❌ 分析失败")


if __name__ == '__main__':
    main()
