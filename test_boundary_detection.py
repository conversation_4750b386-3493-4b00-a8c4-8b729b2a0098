"""
测试改进的边界检测方法
验证基于统计理论的边界检测算法的有效性
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy.stats import norm, multivariate_normal
import sys
from pathlib import Path

# 添加当前目录到路径
sys.path.append(str(Path(__file__).parent))

# 导入我们的分析器
try:
    from probability_density_analysis import ProbabilityDensityAnalyzer
except ImportError as e:
    print(f"导入失败: {e}")
    print("请确保 13.probability_density_analysis.py 文件存在")
    sys.exit(1)

def generate_synthetic_data():
    """生成合成数据用于测试边界检测算法"""
    np.random.seed(42)
    
    # 生成两个不同密度区域的数据
    # 区域1：高密度核心区域
    core_data = np.random.multivariate_normal([0, 0], [[1, 0], [0, 1]], 500)
    
    # 区域2：低密度外围区域
    outer_data = np.random.multivariate_normal([0, 0], [[4, 0], [0, 4]], 200)
    
    # 合并数据
    all_data = np.vstack([core_data, outer_data])
    
    return all_data

def test_boundary_detection_methods():
    """测试不同的边界检测方法"""
    print("=== 边界检测方法测试 ===")
    
    # 生成测试数据
    test_data = generate_synthetic_data()
    x_data, y_data = test_data[:, 0], test_data[:, 1]
    
    # 创建分析器实例
    analyzer = ProbabilityDensityAnalyzer(debug=True)
    
    # 选择一个扇区进行测试（0度方向）
    sector_angle = 0
    angle_width = np.pi / 4  # 45度扇区宽度
    
    # 筛选扇区内的数据点
    angles = np.arctan2(y_data, x_data)
    angle_diff = np.abs(angles - sector_angle)
    angle_diff = np.minimum(angle_diff, 2*np.pi - angle_diff)
    
    sector_mask = angle_diff <= angle_width / 2
    sector_x = x_data[sector_mask]
    sector_y = y_data[sector_mask]
    
    if len(sector_x) < 10:
        print("扇区内数据点太少，无法进行测试")
        return
    
    print(f"扇区内数据点数量: {len(sector_x)}")
    
    # 计算距离和密度
    distances = np.sqrt(sector_x**2 + sector_y**2)
    
    # 创建距离网格和密度估计
    max_distance = np.percentile(distances, 95)
    distance_grid = np.linspace(0, max_distance, 100)
    
    # 使用核密度估计
    from scipy.stats import gaussian_kde
    if len(distances) > 5:
        kde = gaussian_kde(distances)
        density_values = kde(distance_grid)
        
        # 平滑处理
        from scipy.ndimage import gaussian_filter1d
        smoothed_density = gaussian_filter1d(density_values, sigma=1.5)
        
        print("\n--- 测试各种边界检测方法 ---")
        
        # 测试信息论方法
        result_info = analyzer._detect_boundary_information_theory(distance_grid, smoothed_density)
        if result_info:
            print(f"信息论方法: 边界距离={result_info['distance']:.3f}, "
                  f"分数={result_info['score']:.3f}, 置信度={result_info['confidence']:.3f}")
        else:
            print("信息论方法: 未检测到边界")
        
        # 测试统计显著性方法
        result_stat = analyzer._detect_boundary_statistical_significance(distance_grid, smoothed_density)
        if result_stat:
            print(f"统计显著性方法: 边界距离={result_stat['distance']:.3f}, "
                  f"分数={result_stat['score']:.3f}, 置信度={result_stat['confidence']:.3f}")
        else:
            print("统计显著性方法: 未检测到边界")
        
        # 测试自适应阈值方法
        result_adaptive = analyzer._detect_boundary_adaptive_threshold(distance_grid, smoothed_density)
        if result_adaptive:
            print(f"自适应阈值方法: 边界距离={result_adaptive['distance']:.3f}, "
                  f"分数={result_adaptive['score']:.3f}, 置信度={result_adaptive['confidence']:.3f}")
        else:
            print("自适应阈值方法: 未检测到边界")
        
        # 测试贝叶斯方法
        result_bayesian = analyzer._detect_boundary_bayesian(distance_grid, smoothed_density)
        if result_bayesian:
            print(f"贝叶斯方法: 边界距离={result_bayesian['distance']:.3f}, "
                  f"分数={result_bayesian['score']:.3f}, 置信度={result_bayesian['confidence']:.3f}")
        else:
            print("贝叶斯方法: 未检测到边界")
        
        # 测试综合方法
        final_boundary = analyzer._detect_boundary_multi_strategy(distance_grid, smoothed_density)
        if final_boundary:
            print(f"\n最终选择的边界距离: {final_boundary:.3f}")
        else:
            print("\n未能检测到最终边界")
        
        # 可视化结果
        visualize_boundary_detection(distance_grid, smoothed_density, distances, 
                                   result_info, result_stat, result_adaptive, result_bayesian, final_boundary)
    
    else:
        print("数据点太少，无法进行核密度估计")

def visualize_boundary_detection(distance_grid, smoothed_density, raw_distances, 
                               result_info, result_stat, result_adaptive, result_bayesian, final_boundary):
    """可视化边界检测结果"""
    plt.figure(figsize=(15, 10))
    
    # 子图1：密度曲线和检测到的边界
    plt.subplot(2, 2, 1)
    plt.plot(distance_grid, smoothed_density, 'b-', linewidth=2, label='平滑密度曲线')
    
    colors = ['red', 'green', 'orange', 'purple']
    methods = ['信息论', '统计显著性', '自适应阈值', '贝叶斯']
    results = [result_info, result_stat, result_adaptive, result_bayesian]
    
    for i, (result, color, method) in enumerate(zip(results, colors, methods)):
        if result:
            plt.axvline(x=result['distance'], color=color, linestyle='--', 
                       label=f'{method} (置信度: {result["confidence"]:.2f})')
    
    if final_boundary:
        plt.axvline(x=final_boundary, color='black', linewidth=3, 
                   label=f'最终边界: {final_boundary:.2f}')
    
    plt.xlabel('距离')
    plt.ylabel('密度')
    plt.title('边界检测结果对比')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 子图2：原始数据分布
    plt.subplot(2, 2, 2)
    plt.hist(raw_distances, bins=30, alpha=0.7, density=True, label='原始数据分布')
    plt.plot(distance_grid, smoothed_density, 'r-', linewidth=2, label='核密度估计')
    plt.xlabel('距离')
    plt.ylabel('密度')
    plt.title('数据分布与密度估计')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 子图3：方法置信度对比
    plt.subplot(2, 2, 3)
    confidences = []
    method_names = []
    for result, method in zip(results, methods):
        if result:
            confidences.append(result['confidence'])
            method_names.append(method)
    
    if confidences:
        plt.bar(method_names, confidences, color=['red', 'green', 'orange', 'purple'][:len(confidences)])
        plt.ylabel('置信度')
        plt.title('各方法置信度对比')
        plt.xticks(rotation=45)
        plt.grid(True, alpha=0.3)
    
    # 子图4：理论说明
    plt.subplot(2, 2, 4)
    plt.text(0.1, 0.9, '理论基础说明:', fontsize=12, fontweight='bold', transform=plt.gca().transAxes)
    plt.text(0.1, 0.8, '• 信息论方法: 基于KL散度的变点检测', fontsize=10, transform=plt.gca().transAxes)
    plt.text(0.1, 0.7, '• 统计显著性: 基于t检验的显著差异检测', fontsize=10, transform=plt.gca().transAxes)
    plt.text(0.1, 0.6, '• 自适应阈值: 基于Otsu方法的类间方差最大化', fontsize=10, transform=plt.gca().transAxes)
    plt.text(0.1, 0.5, '• 贝叶斯方法: 基于似然比的变点检测', fontsize=10, transform=plt.gca().transAxes)
    plt.text(0.1, 0.3, '优势:', fontsize=12, fontweight='bold', transform=plt.gca().transAxes)
    plt.text(0.1, 0.2, '• 有坚实的数学理论基础', fontsize=10, transform=plt.gca().transAxes)
    plt.text(0.1, 0.1, '• 自动确定阈值，减少主观性', fontsize=10, transform=plt.gca().transAxes)
    plt.text(0.1, 0.0, '• 提供置信度评估', fontsize=10, transform=plt.gca().transAxes)
    plt.axis('off')
    
    plt.tight_layout()
    plt.savefig('boundary_detection_test_results.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    test_boundary_detection_methods()
    print("\n测试完成！结果已保存为 'boundary_detection_test_results.png'")
