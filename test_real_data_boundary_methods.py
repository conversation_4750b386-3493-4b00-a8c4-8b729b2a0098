"""
使用真实数据测试边界检测方法
直接加载真实数据，测试四种理论方法，显示评价指标
"""

import pickle
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from scipy.stats import gaussian_kde, ttest_ind, entropy
from scipy.ndimage import gaussian_filter1d
import math

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class RealDataBoundaryTester:
    """真实数据边界检测测试器"""
    
    def __init__(self):
        self.methods = {
            'information_theory': '信息论方法',
            'statistical_significance': '统计显著性方法', 
            'adaptive_threshold': '自适应阈值方法',
            'bayesian_changepoint': '贝叶斯方法'
        }
        
    def load_real_data(self):
        """加载真实数据"""
        try:
            # 尝试从不同路径加载crossing数据
            crossing_files = [
                Path('result/2024_1/crossing_avoidance_scenes.pkl'),
                Path('result/2024_2/crossing_avoidance_scenes.pkl'),
                Path('result/2024_3/crossing_avoidance_scenes.pkl'),
                Path('crossing_avoidance_scenes.pkl')
            ]

            crossing_data = []
            for crossing_file in crossing_files:
                if crossing_file.exists():
                    with open(crossing_file, 'rb') as f:
                        data = pickle.load(f)
                        crossing_data.extend(data)
                        print(f"✅ 加载 {crossing_file}: {len(data)} 个场景")

            if not crossing_data:
                print("❌ 未找到任何crossing数据文件")
                return None

            print(f"✅ 总共加载 {len(crossing_data)} 个场景")
                
            # 提取相对位置数据
            all_positions = []
            for scene in crossing_data:  # 取前100个场景避免数据过多
                if 'maneuver_moment' in scene:
                    moment = scene['maneuver_moment']
                    if 'relative_positions' in moment:
                        rel_pos = moment['relative_positions']
                        if len(rel_pos) > 0:
                            # 计算距离
                            distances = [math.sqrt(pos['relative_x']**2 + pos['relative_y']**2) 
                                       for pos in rel_pos if 'relative_x' in pos and 'relative_y' in pos]
                            all_positions.extend(distances)
            
            if len(all_positions) < 50:
                print("❌ 数据点太少，无法进行分析")
                return None
                
            print(f"✅ 提取到 {len(all_positions)} 个距离数据点")
            return np.array(all_positions)
            
        except Exception as e:
            print(f"❌ 加载数据失败: {e}")
            return None
    
    def detect_boundary_information_theory(self, sample_distances, smoothed_density):
        """信息论方法"""
        try:
            n = len(smoothed_density)
            if n < 10:
                return None
            
            max_kl_div = 0
            best_changepoint = None
            
            start_idx = int(n * 0.2)
            end_idx = int(n * 0.8)
            
            for i in range(start_idx, end_idx):
                left_density = smoothed_density[:i+1]
                right_density = smoothed_density[i+1:]
                
                if len(left_density) < 3 or len(right_density) < 3:
                    continue
                
                # 归一化为概率分布
                left_prob = left_density / np.sum(left_density)
                right_prob = right_density / np.sum(right_density)
                
                # 计算整体分布
                overall_prob = smoothed_density / np.sum(smoothed_density)
                left_overall = overall_prob[:i+1]
                right_overall = overall_prob[i+1:]
                
                # 计算KL散度
                kl_left = entropy(left_prob, left_overall) if len(left_prob) == len(left_overall) else 0
                kl_right = entropy(right_prob, right_overall) if len(right_prob) == len(right_overall) else 0
                
                total_kl = (len(left_density) * kl_left + len(right_density) * kl_right) / n
                
                if total_kl > max_kl_div:
                    max_kl_div = total_kl
                    best_changepoint = i
            
            if best_changepoint is not None and max_kl_div > 0.05:
                confidence = min(max_kl_div / 2.0, 1.0)
                return {
                    'distance': sample_distances[best_changepoint],
                    'score': max_kl_div,
                    'confidence': confidence,
                    'kl_divergence': max_kl_div
                }
            return None
            
        except Exception as e:
            print(f"信息论方法失败: {e}")
            return None
    
    def detect_boundary_statistical_significance(self, sample_distances, smoothed_density):
        """统计显著性方法"""
        try:
            n = len(smoothed_density)
            if n < 20:
                return None
            
            max_t_stat = 0
            best_boundary = None
            best_p_value = 1.0
            
            start_idx = int(n * 0.2)
            end_idx = int(n * 0.8)
            
            for i in range(start_idx, end_idx):
                left_sample = smoothed_density[:i+1]
                right_sample = smoothed_density[i+1:]
                
                if len(left_sample) < 5 or len(right_sample) < 5:
                    continue
                
                t_stat, p_value = ttest_ind(left_sample, right_sample)
                
                if abs(t_stat) > max_t_stat and p_value < 0.05:
                    max_t_stat = abs(t_stat)
                    best_boundary = i
                    best_p_value = p_value
            
            if best_boundary is not None:
                confidence = 1 - best_p_value
                return {
                    'distance': sample_distances[best_boundary],
                    'score': max_t_stat,
                    'confidence': confidence,
                    't_statistic': max_t_stat,
                    'p_value': best_p_value
                }
            return None
            
        except Exception as e:
            print(f"统计显著性方法失败: {e}")
            return None
    
    def detect_boundary_adaptive_threshold(self, sample_distances, smoothed_density):
        """自适应阈值方法"""
        try:
            n = len(smoothed_density)
            if n < 10:
                return None
            
            max_variance_ratio = 0
            best_threshold_idx = None
            
            total_mean = np.average(range(n), weights=smoothed_density)
            total_variance = np.average((np.arange(n) - total_mean)**2, weights=smoothed_density)
            
            if total_variance == 0:
                return None
            
            start_idx = int(n * 0.1)
            end_idx = int(n * 0.9)
            
            for t in range(start_idx, end_idx):
                w1 = np.sum(smoothed_density[:t+1])
                w2 = np.sum(smoothed_density[t+1:])
                
                if w1 == 0 or w2 == 0:
                    continue
                
                mu1 = np.average(range(t+1), weights=smoothed_density[:t+1])
                mu2 = np.average(range(t+1, n), weights=smoothed_density[t+1:])
                
                between_class_variance = w1 * w2 * (mu1 - mu2)**2
                variance_ratio = between_class_variance / total_variance
                
                if variance_ratio > max_variance_ratio:
                    max_variance_ratio = variance_ratio
                    best_threshold_idx = t
            
            if best_threshold_idx is not None and max_variance_ratio > 0.1:
                confidence = min(max_variance_ratio, 1.0)
                return {
                    'distance': sample_distances[best_threshold_idx],
                    'score': max_variance_ratio,
                    'confidence': confidence,
                    'between_class_variance': max_variance_ratio
                }
            return None
            
        except Exception as e:
            print(f"自适应阈值方法失败: {e}")
            return None
    
    def detect_boundary_bayesian(self, sample_distances, smoothed_density):
        """贝叶斯方法"""
        try:
            n = len(smoothed_density)
            if n < 15:
                return None
            
            max_likelihood_ratio = 0
            best_changepoint = None
            
            start_idx = int(n * 0.15)
            end_idx = int(n * 0.85)
            
            for cp in range(start_idx, end_idx):
                left_data = smoothed_density[:cp+1]
                right_data = smoothed_density[cp+1:]
                
                if len(left_data) < 5 or len(right_data) < 5:
                    continue
                
                left_mean, left_std = np.mean(left_data), np.std(left_data)
                right_mean, right_std = np.mean(right_data), np.std(right_data)
                overall_mean, overall_std = np.mean(smoothed_density), np.std(smoothed_density)
                
                if left_std == 0 or right_std == 0 or overall_std == 0:
                    continue
                
                log_likelihood_h1 = (
                    -0.5 * len(left_data) * np.log(2 * np.pi * left_std**2) -
                    np.sum((left_data - left_mean)**2) / (2 * left_std**2) -
                    0.5 * len(right_data) * np.log(2 * np.pi * right_std**2) -
                    np.sum((right_data - right_mean)**2) / (2 * right_std**2)
                )
                
                log_likelihood_h0 = (
                    -0.5 * n * np.log(2 * np.pi * overall_std**2) -
                    np.sum((smoothed_density - overall_mean)**2) / (2 * overall_std**2)
                )
                
                likelihood_ratio = log_likelihood_h1 - log_likelihood_h0
                
                if likelihood_ratio > max_likelihood_ratio:
                    max_likelihood_ratio = likelihood_ratio
                    best_changepoint = cp
            
            if best_changepoint is not None and max_likelihood_ratio > 1.0:
                confidence = min(1.0 / (1.0 + np.exp(-max_likelihood_ratio/5.0)), 1.0)
                return {
                    'distance': sample_distances[best_changepoint],
                    'score': max_likelihood_ratio,
                    'confidence': confidence,
                    'log_likelihood_ratio': max_likelihood_ratio
                }
            return None
            
        except Exception as e:
            print(f"贝叶斯方法失败: {e}")
            return None
    
    def test_all_methods(self, distances):
        """测试所有方法"""
        print(f"\n{'='*60}")
        print("使用真实数据测试边界检测方法")
        print(f"{'='*60}")
        
        # 准备数据
        max_distance = np.percentile(distances, 95)
        distance_grid = np.linspace(0, max_distance, 100)
        
        # 核密度估计
        kde = gaussian_kde(distances)
        density_values = kde(distance_grid)
        smoothed_density = gaussian_filter1d(density_values, sigma=1.5)
        
        print(f"数据统计:")
        print(f"  总数据点: {len(distances)}")
        print(f"  距离范围: {np.min(distances):.2f} - {np.max(distances):.2f}")
        print(f"  平均距离: {np.mean(distances):.2f}")
        print(f"  标准差: {np.std(distances):.2f}")
        
        # 测试各种方法
        results = {}
        method_functions = {
            'information_theory': self.detect_boundary_information_theory,
            'statistical_significance': self.detect_boundary_statistical_significance,
            'adaptive_threshold': self.detect_boundary_adaptive_threshold,
            'bayesian_changepoint': self.detect_boundary_bayesian
        }
        
        print(f"\n边界检测结果:")
        print(f"{'方法':<20} {'边界距离':<12} {'置信度':<10} {'评分':<10} {'状态'}")
        print("-" * 70)
        
        for method_key, method_func in method_functions.items():
            try:
                result = method_func(distance_grid, smoothed_density)
                if result:
                    results[method_key] = result
                    print(f"{self.methods[method_key]:<20} "
                          f"{result['distance']:<12.3f} "
                          f"{result['confidence']:<10.3f} "
                          f"{result['score']:<10.3f} "
                          f"✅")
                else:
                    print(f"{self.methods[method_key]:<20} "
                          f"{'N/A':<12} "
                          f"{'N/A':<10} "
                          f"{'N/A':<10} "
                          f"❌")
            except Exception as e:
                print(f"{self.methods[method_key]:<20} "
                      f"{'ERROR':<12} "
                      f"{'ERROR':<10} "
                      f"{'ERROR':<10} "
                      f"❌ {str(e)[:20]}")
        
        # 可视化结果
        if results:
            self.visualize_results(distance_grid, smoothed_density, distances, results)
        
        return results
    
    def visualize_results(self, distance_grid, smoothed_density, raw_distances, results):
        """可视化结果"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('真实数据边界检测结果', fontsize=16, fontweight='bold')
        
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
        
        # 子图1: 密度曲线和边界
        ax1 = axes[0, 0]
        ax1.plot(distance_grid, smoothed_density, 'k-', linewidth=2, label='密度曲线')
        
        for i, (method_key, result) in enumerate(results.items()):
            boundary = result['distance']
            confidence = result['confidence']
            ax1.axvline(x=boundary, color=colors[i], linestyle='--', linewidth=2,
                       label=f'{self.methods[method_key]} ({confidence:.2f})')
        
        ax1.set_xlabel('距离')
        ax1.set_ylabel('密度')
        ax1.set_title('边界检测结果')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 子图2: 原始数据分布
        ax2 = axes[0, 1]
        ax2.hist(raw_distances, bins=50, alpha=0.7, density=True, color='lightblue')
        ax2.plot(distance_grid, smoothed_density, 'r-', linewidth=2, label='核密度估计')
        ax2.set_xlabel('距离')
        ax2.set_ylabel('密度')
        ax2.set_title('原始数据分布')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 子图3: 置信度对比
        ax3 = axes[1, 0]
        if results:
            methods = list(results.keys())
            confidences = [results[m]['confidence'] for m in methods]
            method_names = [self.methods[m] for m in methods]
            
            bars = ax3.bar(range(len(confidences)), confidences, 
                          color=colors[:len(confidences)])
            ax3.set_xticks(range(len(method_names)))
            ax3.set_xticklabels(method_names, rotation=45, ha='right')
            ax3.set_ylabel('置信度')
            ax3.set_title('方法置信度对比')
            ax3.set_ylim(0, 1)
            
            for bar, conf in zip(bars, confidences):
                ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                        f'{conf:.3f}', ha='center', va='bottom')
        
        # 子图4: 边界位置对比
        ax4 = axes[1, 1]
        if results:
            boundaries = [results[m]['distance'] for m in methods]
            
            bars = ax4.bar(range(len(boundaries)), boundaries, 
                          color=colors[:len(boundaries)])
            ax4.set_xticks(range(len(method_names)))
            ax4.set_xticklabels(method_names, rotation=45, ha='right')
            ax4.set_ylabel('边界距离')
            ax4.set_title('检测边界位置')
            
            for bar, boundary in zip(bars, boundaries):
                ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                        f'{boundary:.1f}', ha='center', va='bottom')
        
        plt.tight_layout()
        plt.savefig('real_data_boundary_results.png', dpi=300, bbox_inches='tight')
        print(f"\n📁 结果已保存到: real_data_boundary_results.png")
        plt.show()

def main():
    """主函数"""
    tester = RealDataBoundaryTester()
    
    # 加载真实数据
    distances = tester.load_real_data()
    if distances is None:
        return
    
    # 测试所有方法
    results = tester.test_all_methods(distances)
    
    # 显示详细指标
    if results:
        print(f"\n{'='*60}")
        print("详细评价指标")
        print(f"{'='*60}")
        
        for method_key, result in results.items():
            print(f"\n🔬 {tester.methods[method_key]}:")
            print(f"   边界距离: {result['distance']:.4f}")
            print(f"   置信度: {result['confidence']:.4f}")
            print(f"   评分: {result['score']:.4f}")
            
            # 显示方法特定指标
            if method_key == 'information_theory' and 'kl_divergence' in result:
                print(f"   KL散度: {result['kl_divergence']:.4f}")
            elif method_key == 'statistical_significance':
                if 't_statistic' in result:
                    print(f"   t统计量: {result['t_statistic']:.4f}")
                if 'p_value' in result:
                    print(f"   p值: {result['p_value']:.6f}")
            elif method_key == 'adaptive_threshold' and 'between_class_variance' in result:
                print(f"   类间方差: {result['between_class_variance']:.4f}")
            elif method_key == 'bayesian_changepoint' and 'log_likelihood_ratio' in result:
                print(f"   对数似然比: {result['log_likelihood_ratio']:.4f}")
        
        print(f"\n🎉 测试完成! 共测试了 {len(results)} 种方法")
    else:
        print("❌ 所有方法都失败了")

if __name__ == "__main__":
    main()
