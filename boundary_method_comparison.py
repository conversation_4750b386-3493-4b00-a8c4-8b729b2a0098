"""
边界检测方法对比分析工具
用于测试和可视化不同理论方法的性能和合理性
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy.stats import norm, multivariate_normal, gaussian_kde
from scipy.ndimage import gaussian_filter1d
import pandas as pd
import seaborn as sns
from pathlib import Path
import sys

# 添加当前目录到路径
sys.path.append(str(Path(__file__).parent))

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class BoundaryMethodComparator:
    """边界检测方法对比器"""
    
    def __init__(self):
        self.methods = [
            'information_theory',
            'statistical_significance', 
            'adaptive_threshold',
            'bayesian_changepoint'
        ]
        
        self.method_names = {
            'information_theory': '信息论方法',
            'statistical_significance': '统计显著性方法',
            'adaptive_threshold': '自适应阈值方法',
            'bayesian_changepoint': '贝叶斯方法'
        }
        
        self.method_colors = {
            'information_theory': '#FF6B6B',
            'statistical_significance': '#4ECDC4',
            'adaptive_threshold': '#45B7D1',
            'bayesian_changepoint': '#96CEB4'
        }
    
    def generate_test_scenarios(self):
        """生成不同的测试场景"""
        scenarios = {}
        np.random.seed(42)
        
        # 场景1: 明显的双峰分布
        scenario1_data = np.concatenate([
            np.random.normal(2, 0.5, 300),  # 核心区域
            np.random.normal(6, 1.0, 100)   # 外围区域
        ])
        scenarios['双峰分布'] = scenario1_data[scenario1_data >= 0]
        
        # 场景2: 指数衰减分布
        scenario2_data = np.random.exponential(2, 500)
        scenarios['指数衰减'] = scenario2_data
        
        # 场景3: 混合正态分布
        scenario3_data = np.concatenate([
            np.random.normal(1, 0.3, 200),  # 高密度核心
            np.random.normal(3, 0.8, 150),  # 中密度区域
            np.random.normal(6, 1.5, 100)   # 低密度外围
        ])
        scenarios['混合正态'] = scenario3_data[scenario3_data >= 0]
        
        # 场景4: 带噪声的阶跃分布
        scenario4_core = np.random.uniform(0, 2, 300)
        scenario4_outer = np.random.uniform(4, 8, 100)
        scenario4_noise = np.random.normal(0, 0.1, 400)
        scenario4_data = np.concatenate([scenario4_core, scenario4_outer]) + scenario4_noise
        scenarios['阶跃+噪声'] = scenario4_data[scenario4_data >= 0]
        
        return scenarios
    
    def test_all_methods(self, test_data, scenario_name):
        """测试所有方法在给定数据上的表现"""
        try:
            from probability_density_analysis import ProbabilityDensityAnalyzer
        except ImportError:
            print("无法导入 ProbabilityDensityAnalyzer")
            return None
        
        results = {}
        
        # 准备数据
        distances = np.abs(test_data)  # 转换为距离
        max_distance = np.percentile(distances, 95)
        distance_grid = np.linspace(0, max_distance, 100)
        
        # 核密度估计
        if len(distances) > 5:
            kde = gaussian_kde(distances)
            density_values = kde(distance_grid)
            smoothed_density = gaussian_filter1d(density_values, sigma=1.5)
        else:
            return None
        
        # 测试每种方法
        for method in self.methods:
            try:
                analyzer = ProbabilityDensityAnalyzer(boundary_method=method, debug=False)
                
                # 获取详细分析结果
                detailed_result = analyzer.get_boundary_analysis_detailed(
                    distance_grid, smoothed_density, scenario_name
                )
                
                if detailed_result and detailed_result['methods'].get(method, {}).get('success', False):
                    method_result = detailed_result['methods'][method]
                    results[method] = {
                        'boundary_distance': method_result['boundary_distance'],
                        'confidence': method_result['confidence'],
                        'score': method_result['score'],
                        'metrics': method_result['method_specific_metrics'],
                        'success': True
                    }
                else:
                    results[method] = {'success': False}
                    
            except Exception as e:
                print(f"方法 {method} 测试失败: {e}")
                results[method] = {'success': False, 'error': str(e)}
        
        # 添加数据信息
        results['data_info'] = {
            'scenario_name': scenario_name,
            'distance_grid': distance_grid,
            'smoothed_density': smoothed_density,
            'raw_distances': distances,
            'data_points': len(distances)
        }
        
        return results
    
    def visualize_method_comparison(self, results, save_path=None):
        """可视化方法对比结果"""
        if not results:
            print("没有结果可以可视化")
            return
        
        scenario_name = results['data_info']['scenario_name']
        distance_grid = results['data_info']['distance_grid']
        smoothed_density = results['data_info']['smoothed_density']
        raw_distances = results['data_info']['raw_distances']
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle(f'边界检测方法对比分析 - {scenario_name}', fontsize=16, fontweight='bold')
        
        # 子图1: 密度曲线和检测边界
        ax1 = axes[0, 0]
        ax1.plot(distance_grid, smoothed_density, 'k-', linewidth=2, label='密度曲线')
        
        for method in self.methods:
            if results[method].get('success', False):
                boundary = results[method]['boundary_distance']
                confidence = results[method]['confidence']
                ax1.axvline(x=boundary, color=self.method_colors[method], 
                           linestyle='--', linewidth=2,
                           label=f'{self.method_names[method]} (置信度: {confidence:.2f})')
        
        ax1.set_xlabel('距离')
        ax1.set_ylabel('密度')
        ax1.set_title('边界检测结果')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 子图2: 原始数据分布
        ax2 = axes[0, 1]
        ax2.hist(raw_distances, bins=30, alpha=0.7, density=True, color='lightblue', label='原始数据')
        ax2.plot(distance_grid, smoothed_density, 'r-', linewidth=2, label='核密度估计')
        ax2.set_xlabel('距离')
        ax2.set_ylabel('密度')
        ax2.set_title('数据分布')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 子图3: 置信度对比
        ax3 = axes[0, 2]
        confidences = []
        method_labels = []
        colors = []
        
        for method in self.methods:
            if results[method].get('success', False):
                confidences.append(results[method]['confidence'])
                method_labels.append(self.method_names[method])
                colors.append(self.method_colors[method])
        
        if confidences:
            bars = ax3.bar(range(len(confidences)), confidences, color=colors)
            ax3.set_xticks(range(len(method_labels)))
            ax3.set_xticklabels(method_labels, rotation=45, ha='right')
            ax3.set_ylabel('置信度')
            ax3.set_title('方法置信度对比')
            ax3.set_ylim(0, 1)
            
            # 添加数值标签
            for bar, conf in zip(bars, confidences):
                ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                        f'{conf:.3f}', ha='center', va='bottom')
        
        # 子图4: 方法特定指标对比
        ax4 = axes[1, 0]
        self._plot_method_specific_metrics(ax4, results)
        
        # 子图5: 边界位置对比
        ax5 = axes[1, 1]
        boundaries = []
        method_labels_boundary = []
        colors_boundary = []
        
        for method in self.methods:
            if results[method].get('success', False):
                boundaries.append(results[method]['boundary_distance'])
                method_labels_boundary.append(self.method_names[method])
                colors_boundary.append(self.method_colors[method])
        
        if boundaries:
            bars = ax5.bar(range(len(boundaries)), boundaries, color=colors_boundary)
            ax5.set_xticks(range(len(method_labels_boundary)))
            ax5.set_xticklabels(method_labels_boundary, rotation=45, ha='right')
            ax5.set_ylabel('边界距离')
            ax5.set_title('检测到的边界位置')
            
            # 添加数值标签
            for bar, boundary in zip(bars, boundaries):
                ax5.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                        f'{boundary:.2f}', ha='center', va='bottom')
        
        # 子图6: 理论优势说明
        ax6 = axes[1, 2]
        self._plot_theoretical_advantages(ax6)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"结果已保存到: {save_path}")
        
        plt.show()
    
    def _plot_method_specific_metrics(self, ax, results):
        """绘制方法特定指标"""
        metrics_data = []
        
        for method in self.methods:
            if results[method].get('success', False):
                metrics = results[method].get('metrics', {})
                method_name = self.method_names[method]
                
                for metric_name, value in metrics.items():
                    metrics_data.append({
                        'Method': method_name,
                        'Metric': metric_name,
                        'Value': value
                    })
        
        if metrics_data:
            df = pd.DataFrame(metrics_data)
            # 标准化指标值以便比较
            df['Normalized_Value'] = df.groupby('Metric')['Value'].transform(
                lambda x: (x - x.min()) / (x.max() - x.min()) if x.max() > x.min() else 0
            )
            
            # 创建热力图
            pivot_df = df.pivot(index='Method', columns='Metric', values='Normalized_Value')
            sns.heatmap(pivot_df, annot=True, fmt='.2f', cmap='YlOrRd', ax=ax)
            ax.set_title('方法特定指标对比\n(标准化值)')
        else:
            ax.text(0.5, 0.5, '无可用指标', ha='center', va='center', transform=ax.transAxes)
            ax.set_title('方法特定指标')
    
    def _plot_theoretical_advantages(self, ax):
        """绘制理论优势说明"""
        advantages = {
            '信息论方法': ['基于KL散度', '检测分布变化', '理论严谨'],
            '统计显著性': ['基于t检验', '提供p值', '统计学基础'],
            '自适应阈值': ['Otsu方法', '最大化类间方差', '自动阈值'],
            '贝叶斯方法': ['似然比检验', '概率框架', '不确定性量化']
        }
        
        y_pos = 0.9
        for method, advs in advantages.items():
            ax.text(0.05, y_pos, f'{method}:', fontweight='bold', 
                   transform=ax.transAxes, fontsize=10)
            y_pos -= 0.05
            for adv in advs:
                ax.text(0.1, y_pos, f'• {adv}', transform=ax.transAxes, fontsize=9)
                y_pos -= 0.04
            y_pos -= 0.02
        
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
        ax.set_title('理论优势对比')

def run_comprehensive_comparison():
    """运行全面的方法对比分析"""
    print("=== 边界检测方法全面对比分析 ===")
    
    comparator = BoundaryMethodComparator()
    scenarios = comparator.generate_test_scenarios()
    
    all_results = {}
    
    for scenario_name, test_data in scenarios.items():
        print(f"\n--- 测试场景: {scenario_name} ---")
        print(f"数据点数量: {len(test_data)}")
        
        results = comparator.test_all_methods(test_data, scenario_name)
        if results:
            all_results[scenario_name] = results
            
            # 显示结果摘要
            print("检测结果:")
            for method in comparator.methods:
                if results[method].get('success', False):
                    boundary = results[method]['boundary_distance']
                    confidence = results[method]['confidence']
                    print(f"  {comparator.method_names[method]}: "
                          f"边界={boundary:.3f}, 置信度={confidence:.3f}")
                else:
                    print(f"  {comparator.method_names[method]}: 检测失败")
            
            # 可视化结果
            save_path = f'boundary_comparison_{scenario_name.replace("+", "_")}.png'
            comparator.visualize_method_comparison(results, save_path)
        else:
            print(f"场景 {scenario_name} 测试失败")
    
    # 生成综合报告
    generate_summary_report(all_results, comparator)

def generate_summary_report(all_results, comparator):
    """生成综合分析报告"""
    print("\n=== 综合分析报告 ===")
    
    # 统计各方法的成功率
    method_success_count = {method: 0 for method in comparator.methods}
    total_scenarios = len(all_results)
    
    for scenario_name, results in all_results.items():
        for method in comparator.methods:
            if results[method].get('success', False):
                method_success_count[method] += 1
    
    print("\n方法成功率统计:")
    for method in comparator.methods:
        success_rate = method_success_count[method] / total_scenarios * 100
        print(f"  {comparator.method_names[method]}: {success_rate:.1f}% ({method_success_count[method]}/{total_scenarios})")
    
    # 平均置信度统计
    print("\n平均置信度统计:")
    for method in comparator.methods:
        confidences = []
        for results in all_results.values():
            if results[method].get('success', False):
                confidences.append(results[method]['confidence'])
        
        if confidences:
            avg_confidence = np.mean(confidences)
            print(f"  {comparator.method_names[method]}: {avg_confidence:.3f}")
        else:
            print(f"  {comparator.method_names[method]}: 无有效结果")

if __name__ == "__main__":
    run_comprehensive_comparison()
    print("\n分析完成！各场景的详细结果已保存为图片文件。")
